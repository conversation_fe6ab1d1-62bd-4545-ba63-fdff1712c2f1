<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#667eea">
    <title>新增待办事项</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container add-page">
        <header class="add-header">
            <button class="back-btn" onclick="goBack()">
                <span class="back-icon">←</span>
            </button>
            <h1>新增待办</h1>
            <button class="save-btn" id="saveNewTodo">保存</button>
        </header>
        
        <div class="add-form">
            <div class="form-group">
                <label for="todoTitle">任务名称</label>
                <input type="text" id="todoTitle" placeholder="请输入任务名称..." maxlength="50" required>
                <div class="char-count">
                    <span id="titleCount">0</span>/50
                </div>
            </div>
            
            <div class="form-group">
                <label for="todoNote">任务备注</label>
                <textarea id="todoNote" placeholder="请输入任务备注（可选）..." maxlength="200" rows="4"></textarea>
                <div class="char-count">
                    <span id="noteCount">0</span>/200
                </div>
            </div>
            
            <div class="form-group">
                <label for="todoPriority">优先级</label>
                <div class="priority-selector">
                    <button class="priority-btn" data-priority="low">低</button>
                    <button class="priority-btn active" data-priority="normal">普通</button>
                    <button class="priority-btn" data-priority="high">高</button>
                    <button class="priority-btn" data-priority="urgent">紧急</button>
                </div>
            </div>
            
            <div class="form-group">
                <label for="todoCategory">分类</label>
                <select id="todoCategory">
                    <option value="personal">个人</option>
                    <option value="work">工作</option>
                    <option value="study">学习</option>
                    <option value="life">生活</option>
                    <option value="other">其他</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="todoDueDate">截止日期（可选）</label>
                <input type="date" id="todoDueDate">
            </div>
        </div>
        
        <div class="form-actions">
            <button class="btn-cancel" onclick="goBack()">取消</button>
            <button class="btn-save" id="saveNewTodoBottom">保存待办</button>
        </div>
    </div>
    
    <script src="add.js"></script>
</body>
</html>