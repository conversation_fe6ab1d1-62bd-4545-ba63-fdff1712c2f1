<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#667eea">
    <title>待办事项管理</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>📝 待办事项</h1>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="搜索任务..." class="search-input">
                <button class="search-clear" id="searchClear" style="display: none;">×</button>
            </div>
        </header>
        
        <!-- 移除原来的 quick-add-section -->
        
        <div class="filter-section">
            <button class="filter-btn active" data-filter="all">全部</button>
            <button class="filter-btn" data-filter="pending">待完成</button>
            <button class="filter-btn" data-filter="completed">已完成</button>
            <button class="action-btn" onclick="app.markAllCompleted()" title="全部完成">✓</button>
            <button class="action-btn" onclick="app.clearCompleted()" title="清除已完成">🗑️</button>
            <button class="action-btn" onclick="app.exportData()" title="导出数据">📤</button>
        </div>
        
        <div class="category-filter">
            <select id="categoryFilter">
                <option value="all">所有分类</option>
                <option value="personal">个人</option>
                <option value="work">工作</option>
                <option value="study">学习</option>
                <option value="life">生活</option>
                <option value="other">其他</option>
            </select>
        </div>
        
        <div class="todo-list" id="todoList">
            <!-- 待办事项将在这里动态生成 -->
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <span class="stat-number" id="totalCountNum">0</span>
                <span class="stat-label">总计</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="pendingCountNum">0</span>
                <span class="stat-label">待完成</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="completedCountNum">0</span>
                <span class="stat-label">已完成</span>
            </div>
        </div>
    </div>
    
    <!-- 浮动添加按钮 -->
    <button class="fab" onclick="goToAddPage()" title="添加新待办">
        <span class="fab-icon">+</span>
    </button>
    
    <!-- 详情模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="detailTitle">待办详情</h3>
                <button class="close-btn" id="closeDetailBtn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="detail-item">
                    <label>任务名称:</label>
                    <div id="detailTodoTitle" class="detail-value"></div>
                </div>
                <div class="detail-item">
                    <label>任务备注:</label>
                    <div id="detailTodoNote" class="detail-value"></div>
                </div>
                <div class="detail-item">
                    <label>优先级:</label>
                    <div id="detailPriority" class="detail-value"></div>
                </div>
                <div class="detail-item">
                    <label>分类:</label>
                    <div id="detailCategory" class="detail-value"></div>
                </div>
                <div class="detail-item">
                    <label>截止日期:</label>
                    <div id="detailDueDate" class="detail-value"></div>
                </div>
                <div class="detail-item">
                    <label>创建时间:</label>
                    <div id="detailCreatedAt" class="detail-value"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="editDetailBtn">编辑</button>
                <button class="btn-danger" id="deleteDetailBtn">删除</button>
            </div>
        </div>
    </div>
    
    <!-- 编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑待办</h3>
                <button class="close-btn" id="closeEditBtn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="editTitle">任务名称:</label>
                    <input type="text" id="editTitle" maxlength="50" required>
                </div>
                <div class="form-group">
                    <label for="editNote">任务备注:</label>
                    <textarea id="editNote" maxlength="200" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="editPriority">优先级:</label>
                    <select id="editPriority">
                        <option value="low">低</option>
                        <option value="normal">普通</option>
                        <option value="high">高</option>
                        <option value="urgent">紧急</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editCategory">分类:</label>
                    <select id="editCategory">
                        <option value="personal">个人</option>
                        <option value="work">工作</option>
                        <option value="study">学习</option>
                        <option value="life">生活</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editDueDate">截止日期:</label>
                    <input type="date" id="editDueDate">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancelEditBtn">取消</button>
                <button class="btn-primary" id="saveEditBtn">保存</button>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>