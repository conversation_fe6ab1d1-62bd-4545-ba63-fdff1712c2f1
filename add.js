class AddTodoApp {
  constructor() {
    this.selectedPriority = 'normal';
    this.initElements();
    this.bindEvents();
    this.updateCharCounts();
  }

  initElements() {
    // 表单元素
    this.titleInput = document.getElementById('todoTitle');
    this.noteTextarea = document.getElementById('todoNote');
    this.priorityBtns = document.querySelectorAll('.priority-btn');
    this.categorySelect = document.getElementById('todoCategory');
    this.dueDateInput = document.getElementById('todoDueDate');

    // 字符计数
    this.titleCount = document.getElementById('titleCount');
    this.noteCount = document.getElementById('noteCount');

    // 保存按钮
    this.saveBtn = document.getElementById('saveNewTodo');
    this.saveBottomBtn = document.getElementById('saveNewTodoBottom');

    // 设置最小日期为今天
    const today = new Date().toISOString().split('T')[0];
    this.dueDateInput.min = today;
  }

  bindEvents() {
    // 字符计数
    this.titleInput.addEventListener('input', () => this.updateCharCounts());
    this.noteTextarea.addEventListener('input', () => this.updateCharCounts());

    // 优先级选择
    this.priorityBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.preventDefault();
        this.selectPriority(btn.dataset.priority);
      });
    });

    // 保存按钮
    this.saveBtn.addEventListener('click', (e) => {
      e.preventDefault();
      this.saveTodo();
    });

    this.saveBottomBtn.addEventListener('click', (e) => {
      e.preventDefault();
      this.saveTodo();
    });

    // 表单验证
    this.titleInput.addEventListener('blur', () => this.validateTitle());

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.goBack();
      } else if ((e.metaKey || e.ctrlKey) && e.key === 's') {
        e.preventDefault();
        this.saveTodo();
      }
    });

    // 防止表单默认提交
    document.addEventListener('submit', (e) => {
      e.preventDefault();
    });
  }

  updateCharCounts() {
    const titleLength = this.titleInput.value.length;
    const noteLength = this.noteTextarea.value.length;

    this.titleCount.textContent = titleLength;
    this.noteCount.textContent = noteLength;

    // 更新样式
    this.titleCount.style.color = titleLength > 45 ? '#dc3545' : titleLength > 40 ? '#ffc107' : '#6c757d';
    this.noteCount.style.color = noteLength > 180 ? '#dc3545' : noteLength > 160 ? '#ffc107' : '#6c757d';

    // 更新保存按钮状态
    this.updateSaveButtonState();
  }

  selectPriority(priority) {
    this.selectedPriority = priority;

    // 更新按钮状态
    this.priorityBtns.forEach(btn => {
      btn.classList.remove('active');
      if (btn.dataset.priority === priority) {
        btn.classList.add('active');
      }
    });

    // 触觉反馈
    if (navigator.vibrate) {
      navigator.vibrate(10);
    }
  }

  validateTitle() {
    const title = this.titleInput.value.trim();
    const titleGroup = this.titleInput.closest('.form-group');

    // 移除之前的错误状态
    titleGroup.classList.remove('error');
    const existingError = titleGroup.querySelector('.error-message');
    if (existingError) {
      existingError.remove();
    }

    if (!title) {
      titleGroup.classList.add('error');
      const errorMsg = document.createElement('div');
      errorMsg.className = 'error-message';
      errorMsg.textContent = '请输入任务名称';
      titleGroup.appendChild(errorMsg);
      return false;
    }

    return true;
  }

  updateSaveButtonState() {
    const title = this.titleInput.value.trim();
    const isValid = title.length > 0 && title.length <= 50;

    this.saveBtn.disabled = !isValid;
    this.saveBottomBtn.disabled = !isValid;

    if (isValid) {
      this.saveBtn.classList.remove('disabled');
      this.saveBottomBtn.classList.remove('disabled');
    } else {
      this.saveBtn.classList.add('disabled');
      this.saveBottomBtn.classList.add('disabled');
    }
  }

  saveTodo() {
    if (!this.validateTitle()) {
      this.showToast('请输入有效的任务名称', 'error');
      this.titleInput.focus();
      return;
    }

    const title = this.titleInput.value.trim();
    const note = this.noteTextarea.value.trim();
    const category = this.categorySelect.value;
    const dueDate = this.dueDateInput.value || null;

    // 创建新的待办事项
    const newTodo = {
      id: Date.now().toString(),
      title: title,
      note: note,
      completed: false,
      priority: this.selectedPriority,
      category: category,
      dueDate: dueDate,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // 保存到localStorage
    const todos = JSON.parse(localStorage.getItem('todos')) || [];
    todos.unshift(newTodo); // 添加到开头
    localStorage.setItem('todos', JSON.stringify(todos));

    // 显示成功提示
    this.showToast('待办事项添加成功！', 'success');

    // 触觉反馈
    if (navigator.vibrate) {
      navigator.vibrate([50, 30, 50]);
    }

    // 延迟返回主页面
    setTimeout(() => {
      this.goBack();
    }, 1000);
  }

  goBack() {
    // 检查是否有未保存的内容
    const hasContent = this.titleInput.value.trim() || this.noteTextarea.value.trim();

    if (hasContent) {
      if (confirm('您有未保存的内容，确定要离开吗？')) {
        window.location.href = 'index.html';
      }
    } else {
      window.location.href = 'index.html';
    }
  }

  showToast(message, type = 'info') {
    // 移除现有的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
      existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${this.getToastIcon(type)}</span>
                <span class="toast-message">${message}</span>
            </div>
        `;

    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => toast.classList.add('show'), 100);

    // 自动隐藏
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }

  getToastIcon(type) {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type] || icons.info;
  }
}

// 全局函数
function goBack() {
  if (window.addApp) {
    window.addApp.goBack();
  } else {
    window.location.href = 'index.html';
  }
}

// 初始化应用
window.addApp = new AddTodoApp();

// PWA支持
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}