class TodoApp {
    constructor() {
        this.todos = this.migrateTodos(JSON.parse(localStorage.getItem('todos')) || []);
        this.currentFilter = 'all';
        this.currentCategory = 'all';
        this.editingId = null;
        this.viewingId = null;
        
        this.initElements();
        this.bindEvents();
        this.render();
    }
    
    // 迁移旧数据格式到新格式
    migrateTodos(todos) {
        return todos.map(todo => {
            if (!todo.title && todo.text) {
                // 旧格式数据迁移
                return {
                    ...todo,
                    title: todo.text,
                    note: '',
                    priority: 'normal',
                    category: 'personal',
                    dueDate: null,
                    updatedAt: todo.createdAt || new Date().toISOString()
                };
            }
            return todo;
        });
    }
    
    initElements() {
        this.todoList = document.getElementById('todoList');
        this.filterBtns = document.querySelectorAll('.filter-btn');
        this.categoryFilter = document.getElementById('categoryFilter');
        this.totalCountNum = document.getElementById('totalCountNum');
        this.pendingCountNum = document.getElementById('pendingCountNum');
        this.completedCountNum = document.getElementById('completedCountNum');
        
        // 详情模态框
        this.detailModal = document.getElementById('detailModal');
        this.detailTitle = document.getElementById('detailTitle');
        this.detailTodoTitle = document.getElementById('detailTodoTitle');
        this.detailTodoNote = document.getElementById('detailTodoNote');
        this.detailPriority = document.getElementById('detailPriority');
        this.detailCategory = document.getElementById('detailCategory');
        this.detailDueDate = document.getElementById('detailDueDate');
        this.detailCreatedAt = document.getElementById('detailCreatedAt');
        this.closeDetailBtn = document.getElementById('closeDetailBtn');
        this.editDetailBtn = document.getElementById('editDetailBtn');
        this.deleteDetailBtn = document.getElementById('deleteDetailBtn');
        
        // 编辑模态框
        this.editModal = document.getElementById('editModal');
        this.editTitle = document.getElementById('editTitle');
        this.editNote = document.getElementById('editNote');
        this.editPriority = document.getElementById('editPriority');
        this.editCategory = document.getElementById('editCategory');
        this.editDueDate = document.getElementById('editDueDate');
        this.saveEditBtn = document.getElementById('saveEditBtn');
        this.cancelEditBtn = document.getElementById('cancelEditBtn');
        this.closeEditBtn = document.getElementById('closeEditBtn');
    }
    
    bindEvents() {
        // 过滤器
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });
        
        // 分类过滤
        this.categoryFilter.addEventListener('change', (e) => {
            this.setCategoryFilter(e.target.value);
        });
        
        // 详情模态框事件
        this.closeDetailBtn.addEventListener('click', () => this.closeDetailModal());
        this.editDetailBtn.addEventListener('click', () => this.editFromDetail());
        this.deleteDetailBtn.addEventListener('click', () => this.deleteFromDetail());
        this.detailModal.addEventListener('click', (e) => {
            if (e.target === this.detailModal) this.closeDetailModal();
        });
        
        // 编辑模态框事件
        this.saveEditBtn.addEventListener('click', () => this.saveEdit());
        this.cancelEditBtn.addEventListener('click', () => this.closeEditModal());
        this.closeEditBtn.addEventListener('click', () => this.closeEditModal());
        this.editModal.addEventListener('click', (e) => {
            if (e.target === this.editModal) this.closeEditModal();
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDetailModal();
                this.closeEditModal();
            }
        });
    }
    
    goToAddPage() {
        window.location.href = 'add.html';
    }
    
    viewTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (!todo) return;
        
        this.viewingId = id;
        this.detailTodoTitle.textContent = todo.title || todo.text || '无标题';
        this.detailTodoNote.textContent = todo.note || '无备注';
        
        // 优先级显示
        this.detailPriority.textContent = this.getPriorityText(todo.priority || 'normal');
        this.detailPriority.className = `priority-badge ${todo.priority || 'normal'}`;
        
        // 分类显示
        this.detailCategory.textContent = this.getCategoryText(todo.category || 'personal');
        
        // 截止日期
        if (todo.dueDate) {
            const dueDate = new Date(todo.dueDate);
            this.detailDueDate.textContent = dueDate.toLocaleDateString('zh-CN');
            
            if (dueDate < new Date() && !todo.completed) {
                this.detailDueDate.className = 'due-date overdue';
            } else {
                this.detailDueDate.className = 'due-date';
            }
        } else {
            this.detailDueDate.textContent = '无截止日期';
            this.detailDueDate.className = '';
        }
        
        // 创建时间
        const createdAt = new Date(todo.createdAt);
        this.detailCreatedAt.textContent = createdAt.toLocaleString('zh-CN');
        
        this.detailModal.style.display = 'block';
    }
    
    editFromDetail() {
        this.closeDetailModal();
        this.editTodo(this.viewingId);
    }
    
    deleteFromDetail() {
        if (confirm('确定要删除这个待办事项吗？')) {
            this.deleteTodo(this.viewingId);
            this.closeDetailModal();
        }
    }
    
    editTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (!todo) return;
        
        this.editingId = id;
        this.editTitle.value = todo.title || todo.text || '';
        this.editNote.value = todo.note || '';
        this.editPriority.value = todo.priority || 'normal';
        this.editCategory.value = todo.category || 'personal';
        this.editDueDate.value = todo.dueDate || '';
        
        this.editModal.style.display = 'block';
        setTimeout(() => {
            this.editTitle.focus();
        }, 100);
    }
    
    saveEdit() {
        const title = this.editTitle.value.trim();
        if (!title) {
            this.showToast('请输入任务名称！');
            return;
        }
        
        const todo = this.todos.find(todo => todo.id === this.editingId);
        if (todo) {
            todo.title = title;
            todo.note = this.editNote.value.trim();
            todo.priority = this.editPriority.value;
            todo.category = this.editCategory.value;
            todo.dueDate = this.editDueDate.value || null;
            todo.updatedAt = new Date().toISOString();
            
            this.saveTodos();
            this.render();
            this.showToast('修改成功！');
        }
        
        this.closeEditModal();
    }
    
    deleteTodo(id) {
        this.todos = this.todos.filter(todo => todo.id !== id);
        this.saveTodos();
        this.render();
        this.showToast('删除成功！');
    }
    
    toggleTodo(id) {
        const todo = this.todos.find(todo => todo.id === id);
        if (todo) {
            todo.completed = !todo.completed;
            todo.updatedAt = new Date().toISOString();
            this.saveTodos();
            this.render();
            
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }
    }
    
    closeDetailModal() {
        this.detailModal.style.display = 'none';
        this.viewingId = null;
    }
    
    closeEditModal() {
        this.editModal.style.display = 'none';
        this.editingId = null;
    }
    
    setFilter(filter) {
        this.currentFilter = filter;
        this.filterBtns.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });
        this.render();
    }
    
    setCategoryFilter(category) {
        this.currentCategory = category;
        this.render();
    }
    
    getFilteredTodos() {
        let filtered = this.todos;
        
        // 状态过滤
        switch (this.currentFilter) {
            case 'pending':
                filtered = filtered.filter(todo => !todo.completed);
                break;
            case 'completed':
                filtered = filtered.filter(todo => todo.completed);
                break;
        }
        
        // 分类过滤
        if (this.currentCategory !== 'all') {
            filtered = filtered.filter(todo => (todo.category || 'personal') === this.currentCategory);
        }
        
        return filtered;
    }
    
    getPriorityText(priority) {
        const priorityMap = {
            low: '低',
            normal: '普通',
            high: '高',
            urgent: '紧急'
        };
        return priorityMap[priority] || '普通';
    }
    
    getCategoryText(category) {
        const categoryMap = {
            personal: '个人',
            work: '工作',
            study: '学习',
            life: '生活',
            other: '其他'
        };
        return categoryMap[category] || '个人';
    }
    
    formatDueDate(dueDate) {
        if (!dueDate) return '';
        
        const due = new Date(dueDate);
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const dueDay = new Date(due.getFullYear(), due.getMonth(), due.getDate());
        
        const diffTime = dueDay - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays < 0) {
            return `逾期 ${Math.abs(diffDays)} 天`;
        } else if (diffDays === 0) {
            return '今天到期';
        } else if (diffDays === 1) {
            return '明天到期';
        } else if (diffDays <= 7) {
            return `${diffDays} 天后到期`;
        } else {
            return due.toLocaleDateString('zh-CN');
        }
    }
    
    // 在 render() 方法中更新待办事项的HTML结构
    render() {
        const filteredTodos = this.getFilteredTodos();
        
        if (filteredTodos.length === 0) {
            this.todoList.innerHTML = `
                <div class="empty-state">
                    <h3>📝</h3>
                    <p>${this.getEmptyMessage()}</p>
                </div>
            `;
        } else {
            this.todoList.innerHTML = filteredTodos.map(todo => {
                const dueDate = this.formatDueDate(todo.dueDate);
                const isOverdue = todo.dueDate && new Date(todo.dueDate) < new Date() && !todo.completed;
                const isToday = todo.dueDate && new Date(todo.dueDate).toDateString() === new Date().toDateString();
                
                return `
                    <div class="todo-item ${todo.completed ? 'completed' : ''}" onclick="app.viewTodo(${todo.id})">
                        <input type="checkbox" class="todo-checkbox" ${todo.completed ? 'checked' : ''} 
                               onclick="event.stopPropagation(); app.toggleTodo(${todo.id})">
                        
                        <div class="todo-content">
                            <div class="todo-text">${this.escapeHtml(todo.title)}</div>
                            
                            <div class="todo-meta">
                                <span class="todo-priority ${todo.priority}">${this.getPriorityText(todo.priority)}</span>
                                <span class="todo-category">${this.getCategoryText(todo.category)}</span>
                                ${todo.dueDate ? `<span class="todo-due-date ${isOverdue ? 'overdue' : isToday ? 'today' : ''}">${dueDate}</span>` : ''}
                            </div>
                        </div>
                        
                        <div class="todo-actions">
                            <button class="edit-btn" onclick="event.stopPropagation(); app.editTodo(${todo.id})">编辑</button>
                            <button class="delete-btn" onclick="event.stopPropagation(); app.deleteTodo(${todo.id})">删除</button>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        this.updateStats();
    }
    
    getEmptyMessage() {
        if (this.currentCategory !== 'all') {
            return `${this.getCategoryText(this.currentCategory)}分类下没有${this.currentFilter === 'pending' ? '待完成的' : this.currentFilter === 'completed' ? '已完成的' : ''}事项`;
        }
        
        switch (this.currentFilter) {
            case 'pending':
                return '没有待完成的事项\n休息一下吧！';
            case 'completed':
                return '还没有完成任何事项\n加油完成第一个吧！';
            default:
                return '还没有任何待办事项\n点击上方按钮添加一个吧！';
        }
    }
    
    updateStats() {
        const total = this.todos.length;
        const completed = this.todos.filter(todo => todo.completed).length;
        const pending = total - completed;
        
        this.totalCountNum.textContent = total;
        this.pendingCountNum.textContent = pending;
        this.completedCountNum.textContent = completed;
    }
    
    showToast(message) {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 9999;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    saveTodos() {
        localStorage.setItem('todos', JSON.stringify(this.todos));
    }
}

// 全局函数
function goToAddPage() {
    window.location.href = 'add.html';
}

// 初始化应用
const app = new TodoApp();

// PWA支持
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}