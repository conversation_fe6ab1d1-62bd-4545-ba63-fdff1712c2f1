* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100vh;
  padding: 0;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 100%;
  margin: 0;
  background: white;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: max(env(safe-area-inset-top), 20px) 20px 25px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header h1 {
  font-size: 1.8em;
  font-weight: 600;
  margin: 0;
}

.add-todo-section {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

#todoInput {
  flex: 1;
  padding: 16px 20px;
  border: 2px solid #e8e8e8;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;
  background: #f8f9fa;
  -webkit-appearance: none;
}

#todoInput:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.add-btn-mobile {
  width: 50px;
  height: 50px;
  background: #667eea;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  -webkit-appearance: none;
}

.add-btn-mobile:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
}

.add-icon {
  color: white;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
}

.filter-section {
  padding: 12px 15px;
  background: white;
  display: flex;
  gap: 8px;
  border-bottom: 1px solid #f0f0f0;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  position: fixed;
  top: 85px;
  left: 0;
  right: 0;
  z-index: 99;
}

.filter-section::-webkit-scrollbar {
  display: none;
}

.filter-btn {
  padding: 8px 16px;
  /* 减小内边距 */
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  cursor: pointer;
  font-size: 13px;
  /* 减小字体 */
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
  height: 36px;
  /* 设置固定高度 */
  display: flex;
  align-items: center;
}

.filter-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.filter-btn:active {
  transform: scale(0.98);
}

.todo-list {
  flex: 1;
  padding: 0 15px 15px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  margin-top: 170px;
  margin-bottom: 80px;
  height: calc(100vh - 250px);
}

.todo-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  touch-action: pan-y;
  min-height: 80px;
}

.todo-item:active {
  transform: scale(0.98);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.todo-item.completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.todo-item.completed .todo-text {
  text-decoration: line-through;
  color: #6c757d;
}

.todo-item.swipe-left {
  transform: translateX(-80px);
}

.todo-item .delete-overlay {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 80px;
  background: #dc3545;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.todo-item.swipe-left .delete-overlay {
  transform: translateX(0);
}

.todo-checkbox {
  margin-right: 16px;
  margin-top: 4px;
  width: 22px;
  height: 22px;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  border: 2px solid #ddd;
  border-radius: 6px;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  background: white;
}

.todo-checkbox:checked {
  background: #28a745;
  border-color: #28a745;
}

.todo-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  /* 减小字体 */
  font-weight: bold;
}

.todo-text {
  font-size: 16px;
  line-height: 1.5;
  color: #2d3748;
  font-weight: 500;
  word-break: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin-bottom: 8px;
  text-align: left;
}

.todo-actions {
  display: flex;
  gap: 6px;
  /* 减小间距 */
  margin-left: 8px;
  /* 减少左边距 */
  flex-shrink: 0;
}

.edit-btn,
.delete-btn {
  padding: 6px 12px;
  /* 减小内边距 */
  border: none;
  border-radius: 16px;
  /* 减小圆角 */
  cursor: pointer;
  font-size: 12px;
  /* 减小字体 */
  transition: all 0.3s ease;
  -webkit-appearance: none;
  min-width: 50px;
  /* 减小最小宽度 */
  height: 32px;
  /* 设置固定高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn {
  background: #ffc107;
  color: white;
}

.edit-btn:active {
  background: #e0a800;
  transform: scale(0.95);
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:active {
  background: #c82333;
  transform: scale(0.95);
}

.stats {
  padding: 15px;
  background: #f8f9fa;
  display: flex;
  justify-content: space-around;
  border-top: 1px solid #e9ecef;
  padding-bottom: max(env(safe-area-inset-bottom), 15px);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 97;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 20px;
  /* 减小字体 */
  font-weight: 600;
  color: #667eea;
  margin-bottom: 2px;
  /* 减小间距 */
}

.stat-label {
  font-size: 11px;
  /* 减小字体 */
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.modal-content {
  background-color: white;
  margin: 20% auto;
  padding: 0;
  border-radius: 20px;
  width: 90%;
  max-width: 400px;
  overflow: hidden;
  animation: modalSlideUp 0.3s ease;
}

@keyframes modalSlideUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  padding: 25px 25px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f8f9fa;
  border-radius: 50%;
  font-size: 20px;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e9ecef;
  transform: scale(0.95);
}

#editInput {
  width: 100%;
  padding: 20px 25px;
  border: none;
  font-size: 16px;
  outline: none;
  background: #f8f9fa;
  -webkit-appearance: none;
}

.modal-buttons {
  padding: 20px 25px 25px;
  display: flex;
  gap: 12px;
}

.btn-primary,
.btn-secondary {
  flex: 1;
  padding: 16px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  -webkit-appearance: none;
}

.btn-primary {
  background: #28a745;
  color: white;
}

.btn-primary:active {
  background: #218838;
  transform: scale(0.98);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:active {
  background: #5a6268;
  transform: scale(0.98);
}

.empty-state {
  text-align: center;
  padding: 30px 20px;
  /* 减少内边距 */
  color: #6c757d;
}

.empty-state h3 {
  margin-bottom: 12px;
  font-size: 1.5em;
  color: #adb5bd;
}

.empty-state p {
  font-size: 16px;
  line-height: 1.5;
}

.swipe-hint {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  z-index: 999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.swipe-hint.show {
  opacity: 1;
}

/* 针对小屏幕的额外优化 */
@media (max-width: 480px) {
  header {
    padding: max(env(safe-area-inset-top), 15px) 15px 20px;
  }

  header h1 {
    font-size: 1.6em;
  }

  .add-todo-section {
    padding: 15px;
  }

  .filter-section {
    padding: 12px 15px;
  }

  .todo-list {
    padding: 0 15px 15px;
  }

  .todo-item {
    padding: 16px;
    margin-bottom: 10px;
  }

  .todo-actions {
    flex-direction: column;
    gap: 6px;
  }

  .edit-btn,
  .delete-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 50px;
  }

  .stats {
    padding: 15px;
  }

  .stat-number {
    font-size: 20px;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  header {
    padding: 15px 20px;
  }

  header h1 {
    font-size: 1.5em;
  }

  .add-todo-section {
    padding: 15px 20px;
  }

  .todo-item {
    padding: 15px 20px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }

  .container {
    background: #1a202c;
    color: #e2e8f0;
  }

  .add-todo-section,
  .filter-section {
    background: #1a202c;
    border-color: #2d3748;
  }

  #todoInput {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  #todoInput:focus {
    background: #2d3748;
    border-color: #667eea;
  }

  .todo-item {
    background: #2d3748;
    border-color: #4a5568;
  }

  .todo-text {
    color: #e2e8f0;
  }

  .stats {
    background: #2d3748;
    border-color: #4a5568;
  }

  .modal-content {
    background: #1a202c;
  }

  .modal-header {
    border-color: #2d3748;
  }

  #editInput {
    background: #2d3748;
    color: #e2e8f0;
  }
}


/* 新增页面样式 */
.add-page {
  min-height: 100vh;
}

.add-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: max(env(safe-area-inset-top), 20px) 20px 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.2);
}

.add-header h1 {
  font-size: 1.5em;
  font-weight: 600;
  margin: 0;
}

.save-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.add-form {
  padding: 30px 20px;
  flex: 1;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 16px;
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;
  background: #f8f9fa;
  -webkit-appearance: none;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.char-count {
  text-align: right;
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}

.priority-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.priority-btn {
  padding: 12px 20px;
  border: 2px solid #e8e8e8;
  background: white;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  flex: 1;
  min-width: 70px;
}

.priority-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.priority-btn[data-priority="low"].active {
  background: #28a745;
  border-color: #28a745;
}

.priority-btn[data-priority="high"].active {
  background: #ffc107;
  border-color: #ffc107;
  color: #333;
}

.priority-btn[data-priority="urgent"].active {
  background: #dc3545;
  border-color: #dc3545;
}

.form-actions {
  padding: 20px;
  display: flex;
  gap: 15px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding-bottom: max(env(safe-area-inset-bottom), 20px);
}

.btn-cancel,
.btn-save {
  flex: 1;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-cancel:active {
  background: #5a6268;
  transform: scale(0.98);
}

.btn-save {
  background: #667eea;
  color: white;
}

.btn-save:active {
  background: #5a6fd8;
  transform: scale(0.98);
}

/* 主页面快速添加按钮 */
.fab {
  position: fixed;
  bottom: 100px;
  right: 24px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50%;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-appearance: none;
  outline: none;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

.fab:active {
  transform: scale(0.95);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.6);
}

.fab-icon {
  color: white;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
  user-select: none;
}

@keyframes fabPulse {
  0% {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }

  50% {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6), 0 0 0 10px rgba(102, 126, 234, 0.1);
  }

  100% {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }
}

.fab:focus {
  animation: fabPulse 1s ease-in-out;
}

@media (max-width: 768px) {
  .fab {
    bottom: 90px;
    right: 20px;
  }

  .todo-list {
    margin-top: 160px;
    height: calc(100vh - 240px);
  }

  .fab:hover {
    transform: scale(1.05);
  }

  .fab-icon {
    font-size: 22px;
  }
}

@media (max-width: 480px) {
  header {
    padding: max(env(safe-area-inset-top), 15px) 15px 20px;
  }

  .filter-section {
    top: 75px;
  }

  .category-filter {
    top: 115px;
  }

  .todo-list {
    margin-top: 150px;
    height: calc(100vh - 230px);
  }

  .fab {
    bottom: 85px;
    right: 16px;
    width: 48px;
    height: 48px;
  }

  .fab-icon {
    font-size: 20px;
  }
}

@supports (padding: max(0px)) {
  .fab {
    bottom: max(100px, calc(env(safe-area-inset-bottom) + 80px));
    right: max(24px, env(safe-area-inset-right));
  }

  @media (max-width: 768px) {
    .fab {
      bottom: max(20px, env(safe-area-inset-bottom));
      right: max(20px, env(safe-area-inset-right));
    }
  }

  @media (max-width: 480px) {
    .fab {
      bottom: max(16px, env(safe-area-inset-bottom));
      right: max(16px, env(safe-area-inset-right));
    }
  }
}

@media (prefers-color-scheme: dark) {
  .fab {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
  }

  .fab:hover {
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.7);
  }
}

@media (prefers-contrast: high) {
  .fab {
    background: #667eea;
    border: 2px solid #ffffff;
  }
}

@media (prefers-reduced-motion: reduce) {
  .fab {
    transition: none;
  }

  .fab:focus {
    animation: none;
  }
}

.fab::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.fab:active::before {
  width: 60px;
  height: 60px;
}

.fab {
  pointer-events: auto;
}

@media (max-height: 500px) {
  .fab {
    bottom: 12px;
    right: 12px;
    width: 44px;
    height: 44px;
  }

  .fab-icon {
    font-size: 18px;
  }
}

.add-icon {
  font-size: 20px;
  font-weight: 300;
}

/* 分类过滤器 */
.category-filter {
  padding: 8px 15px 12px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  position: fixed;
  top: 125px;
  left: 0;
  right: 0;
  z-index: 98;
}

.category-filter select {
  width: 100%;
  padding: 8px 12px;
  /* 减小内边距 */
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  /* 减小圆角 */
  font-size: 14px;
  /* 减小字体 */
  background: #f8f9fa;
  outline: none;
  -webkit-appearance: none;
  height: 36px;
  /* 设置固定高度 */
}

/* 待办事项卡片更新 */
.todo-item {
  display: flex;
  flex-direction: column;
  padding: 20px;
  margin-bottom: 12px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.todo-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.todo-main {
  flex: 1;
  margin-left: 16px;
}

.todo-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.todo-note {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.priority-badge,
.category-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-badge.low {
  background: #d4edda;
  color: #155724;
}

.priority-badge.normal {
  background: #e2e3e5;
  color: #383d41;
}

.priority-badge.high {
  background: #fff3cd;
  color: #856404;
}

.priority-badge.urgent {
  background: #f8d7da;
  color: #721c24;
}

.category-badge {
  background: #e7f3ff;
  color: #0066cc;
}

.due-date {
  font-size: 12px;
  color: #dc3545;
  font-weight: 600;
}

.due-date.overdue {
  background: #f8d7da;
  padding: 2px 8px;
  border-radius: 8px;
}

.todo-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 详情模态框 */
.detail-modal {
  max-width: 500px;
}

.detail-content {
  padding: 20px 25px;
}

.detail-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-item label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-item span,
.detail-item p {
  color: #6c757d;
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
}

.detail-actions {
  padding: 20px 25px;
  display: flex;
  gap: 12px;
  border-top: 1px solid #f0f0f0;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.btn-danger:active {
  background: #c82333;
  transform: scale(0.98);
}

/* 编辑表单样式 */
.edit-form {
  padding: 20px 25px;
  max-height: 60vh;
  overflow-y: auto;
}

.edit-form .form-group {
  margin-bottom: 20px;
}

.edit-form label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.edit-form input,
.edit-form textarea,
.edit-form select {
  padding: 12px;
  font-size: 14px;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .add-form {
    padding: 20px 15px;
  }

  .form-actions {
    padding: 15px;
    flex-direction: column;
  }

  .priority-selector {
    flex-direction: column;
  }

  .priority-btn {
    flex: none;
  }

  .todo-actions {
    flex-direction: column;
    gap: 6px;
  }

  .detail-actions {
    flex-direction: column;
  }
}